<?php
// Include Bootstrap 5 Nav Walker
require_once get_template_directory() . '/inc/bootstrap-5-navwalker.php';

// Theme setup
function source1_theme_setup()
{
    // Add theme support
    add_theme_support('post-thumbnails');
    add_theme_support('title-tag');

    // Register navigation menus
    register_nav_menus(array(
        'primary' => __('Primary Menu', 'source1'),
    ));
}
add_action('after_setup_theme', 'source1_theme_setup');

// Enqueue styles and scripts
function source1_enqueue_assets()
{
    // Bootstrap CSS
    wp_enqueue_style('bootstrap', 'https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css');

    // Theme styles - ensure proper loading order
    wp_enqueue_style('source1-style', get_stylesheet_uri());
    wp_enqueue_style('source1-main', get_template_directory_uri() . '/assets/css/main.css', array('source1-style'));

    // Bootstrap JS
    wp_enqueue_script('bootstrap', 'https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js', array('jquery'), null, true);

    // Theme scripts
    wp_enqueue_script('source1-main', get_template_directory_uri() . '/assets/js/main.js', array('jquery'), null, true);
}
add_action('wp_enqueue_scripts', 'source1_enqueue_assets');

// Custom excerpt length
function source1_custom_excerpt_length($length)
{
    return 20; // 20 words
}
add_filter('excerpt_length', 'source1_custom_excerpt_length', 999);

// Add "Read More" link to excerpts
function source1_excerpt_more($more)
{
    return '...';
}
add_filter('excerpt_more', 'source1_excerpt_more');

// Custom pagination function
function source1_pagination()
{
    global $wp_query;

    $big = 999999999;

    echo '<nav aria-label="Page navigation">';
    echo paginate_links(array(
        'base' => str_replace($big, '%#%', esc_url(get_pagenum_link($big))),
        'format' => '?paged=%#%',
        'current' => max(1, get_query_var('paged')),
        'total' => $wp_query->max_num_pages,
        'type' => 'list',
        'prev_text' => '<i class="bi bi-chevron-left"></i> قبلی',
        'next_text' => 'بعدی <i class="bi bi-chevron-right"></i>',
        'show_all' => false,
        'end_size' => 3,
        'mid_size' => 3,
    ));
    echo '</nav>';
}

// Add Bootstrap classes to pagination
function source1_add_bootstrap_pagination_classes($content)
{
    $content = str_replace('<ul class=\'page-numbers\'>', '<ul class="pagination justify-content-center">', $content);
    $content = str_replace('<li>', '<li class="page-item">', $content);
    $content = str_replace('<li class="page-item"><span aria-current=\'page\' class=\'page-numbers current\'>', '<li class="page-item active"><span class="page-link">', $content);
    $content = str_replace('<a class=\'page-numbers\'', '<a class="page-link"', $content);
    $content = str_replace('<span class=\'page-numbers dots\'>', '<span class="page-link">', $content);
    return $content;
}
add_filter('paginate_links', 'source1_add_bootstrap_pagination_classes');
