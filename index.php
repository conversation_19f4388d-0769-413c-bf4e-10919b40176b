<?php get_header(); ?>

<div class="main-content py-5">
    <div class="container">
        <div class="row">
            <div class="col-lg-8 mx-auto">
                <?php if (have_posts()) : ?>
                    <div class="posts-grid">
                        <div class="row g-4">
                            <?php while (have_posts()) : the_post(); ?>
                                <div class="col-md-6 col-lg-4">
                                    <article id="post-<?php the_ID(); ?>" <?php post_class('post-card h-100'); ?>>
                                        <div class="card shadow-sm h-100">
                                            <?php if (has_post_thumbnail()) : ?>
                                                <div class="card-img-wrapper">
                                                    <a href="<?php the_permalink(); ?>">
                                                        <?php the_post_thumbnail('medium', array(
                                                            'class' => 'card-img-top',
                                                            'alt' => get_the_title()
                                                        )); ?>
                                                    </a>
                                                </div>
                                            <?php else : ?>
                                                <div class="card-img-wrapper">
                                                    <a href="<?php the_permalink(); ?>">
                                                        <div class="placeholder d-flex align-items-center justify-content-center bg-light"
                                                            style="height: 200px;">
                                                            <i class="bi bi-image text-muted" style="font-size: 3rem;"></i>
                                                        </div>
                                                    </a>
                                                </div>
                                            <?php endif; ?>

                                            <div class="card-body d-flex flex-column">
                                                <h5 class="card-title">
                                                    <a href="<?php the_permalink(); ?>" class="text-decoration-none text-dark">
                                                        <?php the_title(); ?>
                                                    </a>
                                                </h5>

                                                <div class="card-meta mb-2">
                                                    <small class="text-muted">
                                                        <i class="bi bi-calendar"></i>
                                                        <?php echo get_the_date(); ?>
                                                        <span class="mx-2">•</span>
                                                        <i class="bi bi-person"></i>
                                                        <?php the_author(); ?>
                                                    </small>
                                                </div>

                                                <p class="card-text flex-grow-1">
                                                    <?php echo wp_trim_words(get_the_excerpt(), 20, '...'); ?>
                                                </p>

                                                <div class="mt-auto">
                                                    <a href="<?php the_permalink(); ?>" class="btn btn-primary">
                                                        Read More <i class="bi bi-arrow-right"></i>
                                                    </a>
                                                </div>
                                            </div>
                                        </div>
                                    </article>
                                </div>
                            <?php endwhile; ?>
                        </div>
                    </div>

                    <?php if (function_exists('source1_pagination')) : ?>
                        <div class="pagination-wrapper mt-5">
                            <?php source1_pagination(); ?>
                        </div>
                    <?php endif; ?>

                <?php else : ?>
                    <div class="text-center py-5">
                        <h2>No posts found</h2>
                        <p>Sorry, no posts matched your criteria.</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<?php get_footer(); ?>