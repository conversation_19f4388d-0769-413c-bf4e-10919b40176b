/* Custom styles for Bootstrap cards and pagination using new Coolors palette */

:root {
    /* New Coolors Palette */
    --light-blue: #8ECAE6;
    --medium-blue: #219EBC;
    --dark-navy: #023047;
    --golden-yellow: #FFB703;
    --orange: #FB8500;

    /* Color mappings for compatibility */
    --persian-green: var(--medium-blue);
    --charcoal: var(--dark-navy);
    --saffron: var(--golden-yellow);
    --sandy-brown: var(--orange);
}

/* Post Cards */
.post-card .card {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    border: none;
    background-color: white;
}

.post-card .card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(56, 102, 65, 0.15) !important;
}

.card-img-wrapper {
    position: relative;
    overflow: hidden;
    border-radius: 0.375rem 0.375rem 0 0;
}

.card-img-wrapper img {
    transition: transform 0.3s ease;
    width: 100%;
    height: 200px;
    object-fit: cover;
}

.post-card .card:hover .card-img-wrapper img {
    transform: scale(1.05);
}

.card-title a:hover {
    color: var(--persian-green) !important;
    text-decoration: none;
}

/* Pagination Styles */
.pagination {
    margin-top: 2rem;
}

.page-link {
    color: var(--persian-green);
    border-radius: 0.375rem;
    margin: 0 0.125rem;
}

.page-link:hover {
    color: var(--charcoal);
    background-color: var(--saffron);
    border-color: var(--sandy-brown);
}

.page-item.active .page-link {
    background-color: var(--persian-green);
    border-color: var(--persian-green);
}

.page-item.disabled .page-link {
    color: var(--charcoal);
    pointer-events: none;
    background-color: white;
    border-color: var(--sandy-brown);
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .post-card .card {
        margin-bottom: 1.5rem;
    }
    
    .card-img-wrapper img {
        height: 180px;
    }
}

@media (max-width: 576px) {
    .card-img-wrapper img {
        height: 150px;
    }
}

/* Placeholder image styling */
.card-img-wrapper .placeholder {
    background-color: var(--saffron);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--charcoal);
    font-size: 3rem;
}

/* Meta information styling */
.card-meta {
    font-size: 0.875rem;
}

.card-meta i {
    color: var(--charcoal);
}

/* Button styling */
.btn-primary {
    background-color: var(--persian-green);
    border-color: var(--persian-green);
    transition: all 0.3s ease;
}

.btn-primary:hover {
    background-color: var(--charcoal);
    border-color: var(--charcoal);
    transform: translateY(-1px);
}

/* Main content section */
.main-content {
    background-color: var(--saffron);
}

.posts-grid {
    margin-bottom: 2rem;
}

/* Loading animation */
.post-card {
    animation: fadeInUp 0.6s ease forwards;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Additional responsive improvements */
@media (max-width: 992px) {
    .container {
        padding-left: 15px;
        padding-right: 15px;
    }
}

/* Hover effects for links */
a {
    transition: color 0.3s ease;
}

/* Bootstrap icons */
.bi {
    font-size: 0.875rem;
}
