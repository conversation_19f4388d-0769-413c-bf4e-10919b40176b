/*
Theme Name: AMOO
Description: A modern, responsive WordPress theme for reading platforms and educational websites
Version: 1.0
Author: davoud
*/

:root {
    /* New Coolors Palette */
    --light-blue: #8ECAE6;
    --medium-blue: #219EBC;
    --dark-navy: #023047;
    --golden-yellow: #FFB703;
    --orange: #FB8500;

    /* Theme color mappings */
    --primary-color: var(--medium-blue);
    --secondary-color: var(--light-blue);
    --accent-color: var(--orange);
    --light-bg: #ffffff;
    --light-bg-secondary: var(--light-blue);
    --card-bg: #ffffff;
    --text-primary: var(--dark-navy);
    --text-secondary: #666666;
    --border-color: #e0e0e0;
    --shadow-color: rgba(2, 48, 71, 0.1);
    --dark-bg: var(--dark-navy);

    /* Additional color mappings for compatibility */
    --persian-green: var(--medium-blue);
    --charcoal: var(--dark-navy);
    --saffron: var(--golden-yellow);
    --sandy-brown: var(--orange);
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', sans-serif;
    background: var(--light-bg-secondary);
    color: var(--text-primary);
    line-height: 1.6;
}

/* Header Styles */
.navbar {
    background: rgba(255, 255, 255, 0.15);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    padding: 1rem 0;
    transition: all 0.3s ease;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
}

.navbar::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
    border-radius: inherit;
    pointer-events: none;
}

.navbar-brand {
    font-size: 1.5rem;
    font-weight: 700;
    color: white !important;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.navbar-brand:hover {
    transform: scale(1.05);
    text-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.navbar-nav .nav-item {
    margin: 0 0.25rem;
    position: relative;
}

.navbar-nav .nav-link {
    color: rgba(255, 255, 255, 0.9) !important;
    font-weight: 500;
    padding: 0.75rem 1.5rem;
    border-radius: 2rem;
    position: relative;
    overflow: hidden;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    text-decoration: none;
}

.navbar-nav .nav-link::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.navbar-nav .nav-link:hover::before {
    left: 100%;
}

.navbar-nav .nav-link::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    width: 0;
    height: 2px;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
    transition: all 0.3s ease;
    transform: translateX(-50%);
}

.navbar-nav .nav-link:hover::after {
    width: 80%;
}

.navbar-nav .nav-link:hover {
    color: white !important;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.navbar-nav .nav-link.active {
    background: rgba(255, 255, 255, 0.2);
    color: white !important;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.navbar-toggler {
    border: 1px solid rgba(255, 255, 255, 0.3);
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
}

.navbar-toggler:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: scale(1.1);
}

/* Hero Section */
.hero-section {
    background: linear-gradient(135deg, var(--dark-bg) 0%, var(--dark-navy) 100%);
    padding: 6rem 0;
    position: relative;
    overflow: hidden;
}

.hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at 30% 20%, rgba(99, 102, 241, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 70% 80%, rgba(139, 92, 246, 0.1) 0%, transparent 50%);
}

.hero-content {
    position: relative;
    z-index: 2;
}

.hero-title {
    font-size: 3.5rem;
    font-weight: 700;
    margin-bottom: 1.5rem;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hero-subtitle {
    font-size: 1.25rem;
    color: var(--text-secondary);
    margin-bottom: 2rem;
    max-width: 600px;
}

.btn-primary-custom {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border: none;
    padding: 0.75rem 2rem;
    font-weight: 600;
    border-radius: 0.5rem;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.btn-primary-custom:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(99, 102, 241, 0.3);
}

.btn-outline-custom {
    border: 2px solid var(--primary-color);
    color: var(--primary-color);
    padding: 0.75rem 2rem;
    font-weight: 600;
    border-radius: 0.5rem;
    background: transparent;
    transition: all 0.3s ease;
}

.btn-outline-custom:hover {
    background: var(--primary-color);
    color: white;
}

/* Features Section */
.features-section {
    padding: 6rem 0;
    background: var(--dark-bg);
}

.feature-card {
    background: var(--card-bg);
    border: 1px solid var(--border-color);
    border-radius: 1rem;
    padding: 2rem;
    height: 100%;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.feature-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.feature-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border-radius: 1rem;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 1.5rem;
    font-size: 1.5rem;
    color: white;
}

.feature-title {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: var(--text-primary);
}

.feature-description {
    color: var(--text-secondary);
    line-height: 1.6;
}

/* Stats Section */
.stats-section {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    padding: 4rem 0;
}

.stat-item {
    text-align: center;
    color: white;
}

.stat-number {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.stat-label {
    font-size: 1.1rem;
    opacity: 0.9;
}

/* Testimonials Section */
.testimonials-section {
    padding: 6rem 0;
    background: var(--dark-bg);
}

.testimonial-card {
    background: var(--card-bg);
    border: 1px solid var(--border-color);
    border-radius: 1rem;
    padding: 2rem;
    height: 100%;
}

.testimonial-text {
    font-size: 1.1rem;
    line-height: 1.6;
    margin-bottom: 1.5rem;
    color: var(--text-secondary);
}

.testimonial-author {
    display: flex;
    align-items: center;
}

.author-avatar {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    margin-right: 1rem;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
}

.author-info h6 {
    margin: 0;
    color: var(--text-primary);
    font-weight: 600;
}

.author-info small {
    color: var(--text-secondary);
}

/* CTA Section */
.cta-section {
    background: linear-gradient(135deg, var(--card-bg) 0%, var(--dark-bg) 100%);
    padding: 6rem 0;
    text-align: center;
}

.cta-title {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
}

.cta-subtitle {
    font-size: 1.2rem;
    color: var(--text-secondary);
    margin-bottom: 2rem;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

/* Footer */
.footer {
    background: var(--card-bg);
    border-top: 1px solid var(--border-color);
    padding: 3rem 0 1rem;
}

.footer-links {
    list-style: none;
    padding: 0;
}

.footer-links li {
    margin-bottom: 0.5rem;
}

.footer-links a {
    color: var(--text-secondary);
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-links a:hover {
    color: var(--primary-color);
}

.social-links a {
    color: var(--text-secondary);
    font-size: 1.5rem;
    margin: 0 0.5rem;
    transition: color 0.3s ease;
}

.social-links a:hover {
    color: var(--primary-color);
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero-title {
        font-size: 2.5rem;
    }
    
    .hero-subtitle {
        font-size: 1.1rem;
    }
    
    .stat-number {
        font-size: 2rem;
    }
    
    .cta-title {
        font-size: 2rem;
    }
}

/* WordPress Specific Classes */
.wp-block-group {
    margin-bottom: 2rem;
}

.wp-block-heading {
    margin-bottom: 1rem;
}

.wp-block-paragraph {
    margin-bottom: 1rem;
}

/* Animation Classes */
.fade-in {
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.6s ease;
}

.fade-in.visible {
    opacity: 1;
    transform: translateY(0);
}

/* WordPress Content Styles */
.entry-content {
    color: var(--text-secondary);
}

.entry-content h1,
.entry-content h2,
.entry-content h3,
.entry-content h4,
.entry-content h5,
.entry-content h6 {
    color: var(--text-primary);
    margin-bottom: 1rem;
}

.entry-content p {
    margin-bottom: 1rem;
}

.entry-content a {
    color: var(--primary-color);
    text-decoration: none;
}

.entry-content a:hover {
    color: var(--secondary-color);
    text-decoration: underline;
}

/* Post Navigation */
.post-navigation {
    margin: 2rem 0;
    padding: 2rem 0;
    border-top: 1px solid var(--border-color);
    border-bottom: 1px solid var(--border-color);
}

.nav-links {
    display: flex;
    justify-content: space-between;
}

.nav-previous,
.nav-next {
    flex: 1;
}

.nav-next {
    text-align: right;
}

/* Comments */
.comments-area {
    margin-top: 3rem;
    padding-top: 2rem;
    border-top: 1px solid var(--border-color);
}

.comment-list {
    list-style: none;
    padding: 0;
}

.comment {
    margin-bottom: 2rem;
    padding: 1.5rem;
    background: var(--card-bg);
    border-radius: 0.5rem;
    border: 1px solid var(--border-color);
}

.comment-author {
    font-weight: 600;
    color: var(--text-primary);
}

.comment-meta {
    color: var(--text-secondary);
    font-size: 0.9rem;
    margin-bottom: 1rem;
}

.comment-content {
    color: var(--text-secondary);
}

/* Sidebar */
.sidebar {
    background: var(--card-bg);
    border-radius: 1rem;
    padding: 2rem;
    border: 1px solid var(--border-color);
}

.widget {
    margin-bottom: 2rem;
}

.widget-title {
    color: var(--text-primary);
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 1rem;
}

.widget ul {
    list-style: none;
    padding: 0;
}

.widget li {
    margin-bottom: 0.5rem;
}

.widget a {
    color: var(--text-secondary);
    text-decoration: none;
    transition: color 0.3s ease;
}

.widget a:hover {
    color: var(--primary-color);
}